from typing import Protocol

import numpy as np

from src.detections.free_space import EmptyRectArea
from src.product.passport import Passport


class AnalyzerProtocol(Protocol):
    def get_free_space(self, depth_map: np.ndarray) -> list[EmptyRectArea]: ...

    def get_passport_from_ocr(self, image: np.ndarray) -> Passport: ...


class MockAnalyzer(AnalyzerProtocol):
    def get_free_space(self, depth_map: np.ndarray) -> list[EmptyRectArea]:
        return [EmptyRectArea(0, 0, 100, 100, 74, 70, 90)]

    def get_passport_from_ocr(self, image: np.ndarray) -> Passport:
        return Passport(
            owner_name='PRAXAIR',
            serial_number='P551865F',
            manufacturer_name='JIN DUNN',
            manufacturer_serial_number='P1183035',
            manufacturing_date='03/2015',
            last_test_date='03/2015',
            test_pressure=300.0,
            capacity=50.7,
            original_tare_weight=57.5,
            wall_thickness=5.2,
        )
