import asyncio
from typing import Any, Optional, Type

import numpy as np
import pytest
import pytest_asyncio
from heliovision.camera.base import Frame, FrameQueue, OffThreadAsyncFrameQueue

from src.communication.botko_nodes import BotkoOPCUANode
from src.communication.opcua_nodes import OPCUANodes
from src.detections.free_space import EmptyRectArea
from src.main import flow_iteration, initialize
from src.product.passport import Passport


class MockProfiler:
    def __init__(self, frame_queue: OffThreadAsyncFrameQueue):
        self._frame_queue = frame_queue

    async def start_run(self, *args, **kwargs) -> FrameQueue:
        return self._frame_queue

    def update_settings(self, *args, **kwargs) -> None:
        """Mock update settings method."""
        pass

    def trigger(self) -> None:
        """Mock trigger method."""
        # Simulate triggering the profiler
        pass


default_mapping = {
    bool: False,
    int: 0,
    float: 0.0,
    str: '',
    None: None,
}


class MockOPCClient:
    def __init__(
        self,
        nodes: Type[OPCUANodes] = BotkoOPCUANode,
    ):
        self._initialized = False
        self._nodes = {
            node: default_mapping.get(nodes.get_node(node.name).data_type, None)
            for node in nodes
        }

    async def initialize(
        self,
        heartbeat_send_node: OPCUANodes,
        heartbeat_monitor_node: OPCUANodes,
        heartbeat_send_period: float = 2.0,
        heartbeat_monitor_timeout: float = 2.0,
    ) -> None:
        """Mock initialization of the OPC client."""
        self._initialized = True

    async def send(self, node: OPCUANodes, value: Any) -> None:
        """Mock sending a value to an OPC node."""
        if node not in self._nodes:
            raise ValueError(f'Node {node} not found in mock OPC client')
        # Mock sending logic
        self._nodes[node] = value

    async def receive(self, node: OPCUANodes) -> Any:
        """Mock receiving a value from an OPC node."""
        if node not in self._nodes:
            raise ValueError(f'Node {node} not found in mock OPC client')
        # Mock receiving logic
        return self._nodes[node]

    def set_value(self, node: OPCUANodes, value: Any) -> None:
        """Mock method to set a value for a node."""
        if node not in self._nodes:
            raise ValueError(f'Node {node} not found in mock OPC client')
        self._nodes[node] = value

    def get_value(self, node: OPCUANodes) -> Any:
        """Mock method to get a value for a node."""
        if node not in self._nodes:
            raise ValueError(f'Node {node} not found in mock OPC client')
        return self._nodes[node]

    async def await_for_value(self, node: OPCUANodes, value: Any) -> Any:
        """Mock method to await for a specific value."""
        if node not in self._nodes:
            raise ValueError(f'Node {node} not found in mock OPC client')
        while self._nodes[node] != value:
            await asyncio.sleep(0.5)
        return self._nodes[node]

    async def await_for_nonzero_value(self, node: OPCUANodes, period: float = 0.5) -> Any:
        """Mock method to await for a non-zero value."""
        if node not in self._nodes:
            raise ValueError(f'Node {node} not found in mock OPC client')
        while self._nodes[node] == 0:
            await asyncio.sleep(period)
        return self._nodes[node]


class MockAnalyzer:
    def __init__(self) -> None:
        self._free_spaces: list[EmptyRectArea] = []
        self._passport: Passport = Passport(barcode='12345')

    def add_free_space(self, free_space: EmptyRectArea) -> None:
        """Mock method to add a free space."""
        self._free_spaces.append(free_space)

    def set_passport(self, passport: Passport) -> None:
        """Mock method to add an OCR reading."""
        self._passport = passport

    def get_free_space(self, depth_map: np.ndarray) -> list[EmptyRectArea]:
        """Mock method to get free spaces."""
        return self._free_spaces

    def get_passport_from_ocr(self, image: np.ndarray) -> Passport:
        """Mock method to get OCR readings."""
        return self._passport


class MockGui:
    def __init__(self) -> None:
        self._initialized = False
        self._bottle_passport: Optional[Passport] = None
        self._bottle_image: Optional[np.ndarray] = None

    async def initialize(self) -> None:
        """Mock GUI initialization."""
        self._initialized = True

    async def new_measurement(self, passport: Passport, image: np.ndarray) -> None:
        """Mock method to handle a new measurement."""
        self._bottle_passport = passport
        self._bottle_image = image

    async def clear(self) -> None:
        """Mock method to clear the GUI."""
        self._bottle_passport = None
        self._bottle_image = None


@pytest_asyncio.fixture
async def event_loop():
    """Create a new event loop for each test."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def frame_queue() -> OffThreadAsyncFrameQueue:
    """Fixture to provide an OffThreadAsyncFrameQueue."""
    return OffThreadAsyncFrameQueue()


@pytest_asyncio.fixture
async def mock_profiler(frame_queue: OffThreadAsyncFrameQueue) -> MockProfiler:
    profiler = MockProfiler(frame_queue)
    return profiler


@pytest_asyncio.fixture
async def mock_opc_client() -> MockOPCClient:
    """Fixture to provide a mock OPC client."""
    return MockOPCClient()


@pytest_asyncio.fixture
async def mock_analyzer() -> MockAnalyzer:
    """Fixture to provide a mock analyzer."""
    return MockAnalyzer()


@pytest_asyncio.fixture
async def mock_gui() -> MockGui:
    """Fixture to provide a mock GUI."""
    return MockGui()


@pytest.mark.asyncio
async def test_initialize(
    mock_profiler: MockProfiler, mock_opc_client: MockOPCClient, mock_gui: MockGui
):
    """Test the initialize function"""
    frame_queue = await initialize(mock_profiler, mock_opc_client, mock_gui)
    assert isinstance(frame_queue, OffThreadAsyncFrameQueue)
    assert mock_opc_client._initialized is True
    assert mock_gui._initialized is True


@pytest.mark.asyncio
async def test_main_flow_iteration(
    mock_profiler: MockProfiler,
    frame_queue: OffThreadAsyncFrameQueue,
    mock_opc_client: MockOPCClient,
    mock_analyzer: MockAnalyzer,
    mock_gui: MockGui,
):
    """Test the main flow_iteration function"""
    # Arrange
    mock_frame = Frame()
    mock_frame.image = np.zeros((100, 100), dtype=np.uint8)  # Mock image
    frame_queue.put(mock_frame)  # Add mock frame to the queue
    mock_opc_client.set_value(BotkoOPCUANode.BOTKO_LENGTH, 800)
    mock_opc_client.set_value(BotkoOPCUANode.BOTKO_DIAMETER, 400)
    mock_opc_client.set_value(BotkoOPCUANode.BOTKO_SCAN_REQUEST, True)
    mock_opc_client.set_value(BotkoOPCUANode.BOTKO_PASSPORT_ACKNOWLEDGE, True)
    mock_free_space = EmptyRectArea(u=100, v=200, w=50, h=100, r=150, theta=30, z=10)
    mock_analyzer.add_free_space(mock_free_space)
    mock_passport = Passport(
        barcode='12345',
        diameter=400,
        length=800,
        owner_name='UCAR',
        serial_number='70655',
        manufacturer_name='MANNESMANN',
        manufacturer_serial_number='938838',
        manufacturing_date='10/1987',
        last_test_date='03/2009',
        test_pressure=300.0,
        capacity=51.1,
        original_tare_weight=67.8,
        wall_thickness=5.9,
    )
    mock_analyzer.set_passport(mock_passport)
    # Act
    try:
        await flow_iteration(
            mock_profiler, frame_queue, mock_opc_client, mock_analyzer, mock_gui
        )
    except Exception as e:
        pytest.fail(f'Main flow raised an exception: {e}')
    # Assert
    assert mock_opc_client.get_value(BotkoOPCUANode.HV_SCAN_FINISHED) is True
    assert mock_opc_client.get_value(BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY) is True
    assert mock_opc_client.get_value(BotkoOPCUANode.HV_PASSPORT_READY) is True
    assert mock_opc_client.get_value(BotkoOPCUANode.HV_ENGRAVING_RADIUS) == mock_free_space.r
    assert mock_opc_client.get_value(BotkoOPCUANode.HV_ENGRAVING_ANGLE) == mock_free_space.theta
    assert mock_opc_client.get_value(BotkoOPCUANode.HV_ENGRAVING_HEIGHT) == mock_free_space.z
    assert (
        mock_opc_client.get_value(BotkoOPCUANode.HV_PASSPORT_OWNER_NAME)
        == mock_passport.owner_name
    )
    assert (
        mock_opc_client.get_value(BotkoOPCUANode.HV_PASSPORT_OWNER_CODE)
        == mock_passport.owner_code
    )
    assert (
        mock_opc_client.get_value(BotkoOPCUANode.HV_PASSPORT_SERIAL_NUMBER)
        == mock_passport.serial_number
    )
    assert (
        mock_opc_client.get_value(BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_NAME)
        == mock_passport.manufacturer_name
    )
    assert (
        mock_opc_client.get_value(BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_CODE)
        == mock_passport.manufacturer_code
    )
    assert (
        mock_opc_client.get_value(BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER)
        == mock_passport.manufacturer_serial_number
    )
    assert (
        mock_opc_client.get_value(BotkoOPCUANode.HV_PASSPORT_DATE_MANUFACTURING)
        == mock_passport.manufacturing_date
    )
    assert (
        mock_opc_client.get_value(BotkoOPCUANode.HV_PASSPORT_DATE_LAST_TEST)
        == mock_passport.last_test_date
    )
    assert (
        mock_opc_client.get_value(BotkoOPCUANode.HV_PASSPORT_TEST_PRESSURE)
        == mock_passport.test_pressure
    )
    assert (
        mock_opc_client.get_value(BotkoOPCUANode.HV_PASSPORT_CAPACITY) == mock_passport.capacity
    )
    assert (
        mock_opc_client.get_value(BotkoOPCUANode.HV_PASSPORT_ORIGINAL_TARRA_WEIGHT)
        == mock_passport.original_tare_weight
    )
    assert mock_gui._bottle_passport == mock_passport
    assert mock_gui._bottle_image is not None
