/*
OpenCL RandomForestClassifier
classifier_class_name = ObjectSegmenter
feature_specification = gaussian_blur=5 sobel_of_gaussian_blur=5
num_ground_truth_dimensions = 2
num_classes = 2
num_features = 2
max_depth = 2
num_trees = 100
feature_importances = 0.5460922334613842,0.4539077665386158
positive_class_identifier = 2
apoc_version = 0.12.0
*/
__kernel void predict (IMAGE_in0_TYPE in0, IMAGE_in1_TYPE in1, IMAGE_out_TYPE out) {
 sampler_t sampler = CLK_NORMALIZED_COORDS_FALSE | CLK_ADDRESS_CLAMP_TO_EDGE | CLK_FILTER_NEAREST;
 const int x = get_global_id(0);
 const int y = get_global_id(1);
 const int z = get_global_id(2);
 float i0 = READ_IMAGE(in0, sampler, POS_in0_INSTANCE(x,y,z,0)).x;
 float i1 = READ_IMAGE(in1, sampler, POS_in1_INSTANCE(x,y,z,0)).x;
 float s0=0;
 float s1=0;
if(i0<80.82296752929688){
 if(i1<51.79154968261719){
  s0+=0.9808270449743435;
  s1+=0.019172955025656503;
 } else {
  s0+=0.22183098591549297;
  s1+=0.778169014084507;
 }
} else {
 if(i0<96.69923400878906){
  s0+=0.3468875502008032;
  s1+=0.6531124497991968;
 } else {
  s0+=0.004154929577464789;
  s1+=0.9958450704225352;
 }
}
if(i1<41.448875427246094){
 if(i1<23.53600311279297){
  s0+=0.9790107251480711;
  s1+=0.020989274851928925;
 } else {
  s0+=0.7313220792244693;
  s1+=0.26867792077553065;
 }
} else {
 if(i1<58.93560028076172){
  s0+=0.37982919886132577;
  s1+=0.6201708011386743;
 } else {
  s0+=0.01737366610812241;
  s1+=0.9826263338918776;
 }
}
if(i1<41.73090362548828){
 if(i1<23.23069953918457){
  s0+=0.9802696902725729;
  s1+=0.01973030972742705;
 } else {
  s0+=0.7371815806662312;
  s1+=0.26281841933376876;
 }
} else {
 if(i1<57.19087219238281){
  s0+=0.40221729490022173;
  s1+=0.5977827050997783;
 } else {
  s0+=0.0270481144343303;
  s1+=0.9729518855656697;
 }
}
if(i0<80.91569519042969){
 if(i1<44.41826629638672){
  s0+=0.9837404319633367;
  s1+=0.016259568036663254;
 } else {
  s0+=0.3626733921815889;
  s1+=0.6373266078184111;
 }
} else {
 if(i1<37.934608459472656){
  s0+=0.14548022598870056;
  s1+=0.8545197740112994;
 } else {
  s0+=0.009754484582954451;
  s1+=0.9902455154170455;
 }
}
if(i1<41.39433670043945){
 if(i0<96.68724060058594){
  s0+=0.9829690794310469;
  s1+=0.01703092056895305;
 } else {
  s0+=0.007929997265518185;
  s1+=0.9920700027344819;
 }
} else {
 if(i0<70.01217651367188){
  s0+=0.4852511294180175;
  s1+=0.5147488705819825;
 } else {
  s0+=0.01936264622831787;
  s1+=0.9806373537716822;
 }
}
if(i1<41.30522155761719){
 if(i1<23.58423614501953){
  s0+=0.9792130375828281;
  s1+=0.02078696241717195;
 } else {
  s0+=0.7303485118290511;
  s1+=0.26965148817094886;
 }
} else {
 if(i1<57.19087219238281){
  s0+=0.3980496453900709;
  s1+=0.6019503546099291;
 } else {
  s0+=0.02332907620263942;
  s1+=0.9766709237973605;
 }
}
if(i1<42.08723831176758){
 if(i1<23.199527740478516){
  s0+=0.9798019394765084;
  s1+=0.020198060523491573;
 } else {
  s0+=0.7391549295774648;
  s1+=0.2608450704225352;
 }
} else {
 if(i0<66.65872955322266){
  s0+=0.5211471610660486;
  s1+=0.4788528389339513;
 } else {
  s0+=0.023311648079306073;
  s1+=0.9766883519206939;
 }
}
if(i0<79.99603271484375){
 if(i0<27.744850158691406){
  s0+=0.9893621174266084;
  s1+=0.01063788257339163;
 } else {
  s0+=0.7653987390009007;
  s1+=0.23460126099909928;
 }
} else {
 if(i1<38.096153259277344){
  s0+=0.16422423819492904;
  s1+=0.835775761805071;
 } else {
  s0+=0.01010860484544695;
  s1+=0.989891395154553;
 }
}
if(i1<41.394622802734375){
 if(i0<93.34379577636719){
  s0+=0.9838950648527017;
  s1+=0.01610493514729826;
 } else {
  s0+=0.025095057034220533;
  s1+=0.9749049429657795;
 }
} else {
 if(i1<57.107749938964844){
  s0+=0.3935344827586207;
  s1+=0.6064655172413793;
 } else {
  s0+=0.02291560967360054;
  s1+=0.9770843903263995;
 }
}
if(i0<79.99603271484375){
 if(i1<48.541259765625){
  s0+=0.9826086429774294;
  s1+=0.0173913570225706;
 } else {
  s0+=0.29010349288486414;
  s1+=0.7098965071151359;
 }
} else {
 if(i0<96.95207214355469){
  s0+=0.3526570048309179;
  s1+=0.6473429951690821;
 } else {
  s0+=0.00401520040152004;
  s1+=0.99598479959848;
 }
}
if(i0<79.99671173095703){
 if(i0<29.209848403930664){
  s0+=0.9883274269898342;
  s1+=0.01167257301016581;
 } else {
  s0+=0.7504229496138286;
  s1+=0.2495770503861714;
 }
} else {
 if(i0<96.70414733886719){
  s0+=0.346493265211333;
  s1+=0.6535067347886669;
 } else {
  s0+=0.004852290566576281;
  s1+=0.9951477094334237;
 }
}
if(i0<79.2845458984375){
 if(i1<52.19689178466797){
  s0+=0.9806575468167055;
  s1+=0.019342453183294497;
 } else {
  s0+=0.22629222629222628;
  s1+=0.7737077737077737;
 }
} else {
 if(i1<42.735130310058594){
  s0+=0.15577571669477233;
  s1+=0.8442242833052277;
 } else {
  s0+=0.010735001298588866;
  s1+=0.9892649987014112;
 }
}
if(i1<42.45777130126953){
 if(i1<22.899717330932617){
  s0+=0.9804395943789571;
  s1+=0.01956040562104288;
 } else {
  s0+=0.7352000623976289;
  s1+=0.2647999376023711;
 }
} else {
 if(i1<55.90032958984375){
  s0+=0.3879463155229172;
  s1+=0.6120536844770829;
 } else {
  s0+=0.024975433999344907;
  s1+=0.9750245660006551;
 }
}
if(i0<81.12721252441406){
 if(i0<28.055416107177734){
  s0+=0.9892656729647469;
  s1+=0.010734327035253172;
 } else {
  s0+=0.7589876526366055;
  s1+=0.2410123473633945;
 }
} else {
 if(i1<38.096153259277344){
  s0+=0.15921787709497207;
  s1+=0.840782122905028;
 } else {
  s0+=0.00749375520399667;
  s1+=0.9925062447960034;
 }
}
if(i0<82.41267395019531){
 if(i0<28.480899810791016){
  s0+=0.9890861788617886;
  s1+=0.010913821138211382;
 } else {
  s0+=0.7529709056139872;
  s1+=0.24702909438601284;
 }
} else {
 if(i1<38.184425354003906){
  s0+=0.14515366430260046;
  s1+=0.8548463356973995;
 } else {
  s0+=0.006793478260869565;
  s1+=0.9932065217391305;
 }
}
if(i1<41.66588592529297){
 if(i0<96.94207763671875){
  s0+=0.9832593555125114;
  s1+=0.016740644487488538;
 } else {
  s0+=0.004630890765459003;
  s1+=0.995369109234541;
 }
} else {
 if(i0<70.01118469238281){
  s0+=0.4955846936044956;
  s1+=0.5044153063955045;
 } else {
  s0+=0.01942931468986261;
  s1+=0.9805706853101374;
 }
}
if(i1<41.39668273925781){
 if(i1<21.34554672241211){
  s0+=0.9817500682909063;
  s1+=0.018249931709093746;
 } else {
  s0+=0.7557865557865557;
  s1+=0.2442134442134442;
 }
} else {
 if(i0<70.01217651367188){
  s0+=0.47636849132176234;
  s1+=0.5236315086782376;
 } else {
  s0+=0.02133290560590264;
  s1+=0.9786670943940974;
 }
}
if(i1<41.73308563232422){
 if(i0<96.92715454101562){
  s0+=0.9832413105343809;
  s1+=0.016758689465619148;
 } else {
  s0+=0.005666486778197518;
  s1+=0.9943335132218025;
 }
} else {
 if(i0<74.94324493408203){
  s0+=0.4573096570177572;
  s1+=0.5426903429822427;
 } else {
  s0+=0.015005359056806002;
  s1+=0.984994640943194;
 }
}
if(i1<41.37934875488281){
 if(i1<23.230804443359375){
  s0+=0.9794032452385225;
  s1+=0.020596754761477532;
 } else {
  s0+=0.7349881215695913;
  s1+=0.2650118784304088;
 }
} else {
 if(i0<64.50670623779297){
  s0+=0.5196374622356495;
  s1+=0.48036253776435045;
 } else {
  s0+=0.02874503388642206;
  s1+=0.971254966113578;
 }
}
if(i0<81.27198791503906){
 if(i0<31.756977081298828){
  s0+=0.9875224531691045;
  s1+=0.012477546830895562;
 } else {
  s0+=0.7384479859053416;
  s1+=0.26155201409465845;
 }
} else {
 if(i0<96.95207214355469){
  s0+=0.3326761951700345;
  s1+=0.6673238048299655;
 } else {
  s0+=0.0037137551778317385;
  s1+=0.9962862448221682;
 }
}
if(i0<80.65472412109375){
 if(i1<52.376007080078125){
  s0+=0.9805399528900163;
  s1+=0.019460047109983693;
 } else {
  s0+=0.22213438735177865;
  s1+=0.7778656126482213;
 }
} else {
 if(i0<96.95153045654297){
  s0+=0.34986098239110286;
  s1+=0.6501390176088971;
 } else {
  s0+=0.004526166902404526;
  s1+=0.9954738330975955;
 }
}
if(i0<80.17985534667969){
 if(i0<27.92105484008789){
  s0+=0.9889152707757359;
  s1+=0.011084729224264109;
 } else {
  s0+=0.7626540416347559;
  s1+=0.23734595836524403;
 }
} else {
 if(i0<97.68896484375){
  s0+=0.3573043871551334;
  s1+=0.6426956128448665;
 } else {
  s0+=0.0025533725796155755;
  s1+=0.9974466274203845;
 }
}
if(i1<41.394622802734375){
 if(i0<93.34379577636719){
  s0+=0.9837572169845794;
  s1+=0.016242783015420596;
 } else {
  s0+=0.02854204165595269;
  s1+=0.9714579583440474;
 }
} else {
 if(i1<57.19087219238281){
  s0+=0.3971553610503282;
  s1+=0.6028446389496718;
 } else {
  s0+=0.025376820233330495;
  s1+=0.9746231797666695;
 }
}
if(i0<80.92070770263672){
 if(i0<30.936309814453125){
  s0+=0.9879562231538878;
  s1+=0.012043776846112297;
 } else {
  s0+=0.739511883873026;
  s1+=0.260488116126974;
 }
} else {
 if(i1<42.44886016845703){
  s0+=0.14515795994218458;
  s1+=0.8548420400578154;
 } else {
  s0+=0.00799930440831232;
  s1+=0.9920006955916877;
 }
}
if(i1<41.40599060058594){
 if(i1<23.232383728027344){
  s0+=0.9799287152069951;
  s1+=0.020071284793004858;
 } else {
  s0+=0.7291495985580861;
  s1+=0.2708504014419138;
 }
} else {
 if(i1<57.334144592285156){
  s0+=0.3936869793950022;
  s1+=0.6063130206049978;
 } else {
  s0+=0.025524235132347885;
  s1+=0.9744757648676521;
 }
}
if(i1<41.39433670043945){
 if(i1<20.315196990966797){
  s0+=0.9826840677699595;
  s1+=0.01731593223004049;
 } else {
  s0+=0.7615200100997349;
  s1+=0.23847998990026512;
 }
} else {
 if(i1<55.79243850708008){
  s0+=0.4164265129682997;
  s1+=0.5835734870317003;
 } else {
  s0+=0.02679027045415887;
  s1+=0.9732097295458412;
 }
}
if(i1<41.73090362548828){
 if(i0<96.70057678222656){
  s0+=0.9834691671423886;
  s1+=0.01653083285761135;
 } else {
  s0+=0.006559169171904892;
  s1+=0.9934408308280951;
 }
} else {
 if(i1<57.108394622802734){
  s0+=0.39086986145809677;
  s1+=0.6091301385419032;
 } else {
  s0+=0.021843412712009593;
  s1+=0.9781565872879904;
 }
}
if(i0<81.46023559570312){
 if(i0<28.40994644165039){
  s0+=0.9886097239449059;
  s1+=0.011390276055094128;
 } else {
  s0+=0.7538945162417046;
  s1+=0.2461054837582955;
 }
} else {
 if(i1<38.096153259277344){
  s0+=0.15318744053282587;
  s1+=0.8468125594671742;
 } else {
  s0+=0.01034656796769852;
  s1+=0.9896534320323015;
 }
}
if(i0<80.15615844726562){
 if(i1<53.56987380981445){
  s0+=0.9804333013753972;
  s1+=0.019566698624602787;
 } else {
  s0+=0.21506312581628212;
  s1+=0.7849368741837179;
 }
} else {
 if(i0<97.02656555175781){
  s0+=0.36018735362997656;
  s1+=0.6398126463700234;
 } else {
  s0+=0.004460808610068682;
  s1+=0.9955391913899313;
 }
}
if(i0<80.15615844726562){
 if(i0<28.480899810791016){
  s0+=0.9892589519537428;
  s1+=0.010741048046257163;
 } else {
  s0+=0.756415770609319;
  s1+=0.243584229390681;
 }
} else {
 if(i1<38.10838317871094){
  s0+=0.1649269311064718;
  s1+=0.8350730688935282;
 } else {
  s0+=0.009857612267250822;
  s1+=0.9901423877327492;
 }
}
if(i0<80.87995910644531){
 if(i0<28.427284240722656){
  s0+=0.9892894767646025;
  s1+=0.0107105232353974;
 } else {
  s0+=0.7572222222222222;
  s1+=0.2427777777777778;
 }
} else {
 if(i1<40.204978942871094){
  s0+=0.15673487744546885;
  s1+=0.8432651225545311;
 } else {
  s0+=0.00772692536299567;
  s1+=0.9922730746370043;
 }
}
if(i0<80.92070770263672){
 if(i1<52.38086700439453){
  s0+=0.9811161234608161;
  s1+=0.018883876539183893;
 } else {
  s0+=0.21671566568668627;
  s1+=0.7832843343133137;
 }
} else {
 if(i1<42.436187744140625){
  s0+=0.14563309653155035;
  s1+=0.8543669034684497;
 } else {
  s0+=0.006958762886597939;
  s1+=0.9930412371134021;
 }
}
if(i0<79.31681823730469){
 if(i0<27.636131286621094){
  s0+=0.989543292722653;
  s1+=0.010456707277347058;
 } else {
  s0+=0.7626405463199283;
  s1+=0.23735945368007175;
 }
} else {
 if(i1<37.934608459472656){
  s0+=0.17472807220550798;
  s1+=0.825271927794492;
 } else {
  s0+=0.010421836228287842;
  s1+=0.9895781637717121;
 }
}
if(i1<43.095523834228516){
 if(i1<23.22881317138672){
  s0+=0.9796614735329772;
  s1+=0.020338526467022854;
 } else {
  s0+=0.7172889865076875;
  s1+=0.28271101349231254;
 }
} else {
 if(i0<66.66268157958984){
  s0+=0.4735358596930786;
  s1+=0.5264641403069213;
 } else {
  s0+=0.024071417184760083;
  s1+=0.9759285828152399;
 }
}
if(i0<81.40215301513672){
 if(i1<45.45879364013672){
  s0+=0.9834247417534591;
  s1+=0.016575258246540988;
 } else {
  s0+=0.36457233368532205;
  s1+=0.635427666314678;
 }
} else {
 if(i0<96.63008117675781){
  s0+=0.3412816691505216;
  s1+=0.6587183308494784;
 } else {
  s0+=0.004610226320201173;
  s1+=0.9953897736797989;
 }
}
if(i0<79.99603271484375){
 if(i0<28.434669494628906){
  s0+=0.9889034705889996;
  s1+=0.011096529411000377;
 } else {
  s0+=0.7529974307736226;
  s1+=0.2470025692263774;
 }
} else {
 if(i1<41.18120574951172){
  s0+=0.16017039403620872;
  s1+=0.8398296059637913;
 } else {
  s0+=0.008157022686719348;
  s1+=0.9918429773132806;
 }
}
if(i1<41.096675872802734){
 if(i0<96.70057678222656){
  s0+=0.983444313243332;
  s1+=0.016555686756667926;
 } else {
  s0+=0.005254424778761062;
  s1+=0.994745575221239;
 }
} else {
 if(i0<64.50670623779297){
  s0+=0.5140268848626535;
  s1+=0.4859731151373466;
 } else {
  s0+=0.03038420327567733;
  s1+=0.9696157967243226;
 }
}
if(i0<80.92070770263672){
 if(i1<50.840431213378906){
  s0+=0.9809587136553225;
  s1+=0.019041286344677506;
 } else {
  s0+=0.23576937201615863;
  s1+=0.7642306279838413;
 }
} else {
 if(i1<40.847232818603516){
  s0+=0.14773208986858838;
  s1+=0.8522679101314116;
 } else {
  s0+=0.008762036956710332;
  s1+=0.9912379630432897;
 }
}
if(i1<41.73090362548828){
 if(i0<98.05703735351562){
  s0+=0.9824536904928145;
  s1+=0.017546309507185488;
 } else {
  s0+=0.0008257638315441783;
  s1+=0.9991742361684558;
 }
} else {
 if(i1<57.14959716796875){
  s0+=0.4021471706553344;
  s1+=0.5978528293446657;
 } else {
  s0+=0.02295676188832312;
  s1+=0.9770432381116769;
 }
}
if(i0<80.02987670898438){
 if(i0<28.95671844482422){
  s0+=0.988933927322645;
  s1+=0.01106607267735502;
 } else {
  s0+=0.7576067875950848;
  s1+=0.24239321240491515;
 }
} else {
 if(i0<96.38524627685547){
  s0+=0.354656632173095;
  s1+=0.6453433678269049;
 } else {
  s0+=0.004094888449590511;
  s1+=0.9959051115504095;
 }
}
if(i1<43.00511169433594){
 if(i0<96.70753479003906){
  s0+=0.982721067799181;
  s1+=0.01727893220081905;
 } else {
  s0+=0.0058495081095453335;
  s1+=0.9941504918904547;
 }
} else {
 if(i1<58.91173553466797){
  s0+=0.36258926514627965;
  s1+=0.6374107348537204;
 } else {
  s0+=0.016999389765495598;
  s1+=0.9830006102345044;
 }
}
if(i0<80.31436157226562){
 if(i0<26.618396759033203){
  s0+=0.9901353446414904;
  s1+=0.009864655358509574;
 } else {
  s0+=0.7755570117955439;
  s1+=0.2244429882044561;
 }
} else {
 if(i0<97.2079849243164){
  s0+=0.3378016085790885;
  s1+=0.6621983914209115;
 } else {
  s0+=0.003420996365191362;
  s1+=0.9965790036348087;
 }
}
if(i1<43.04206085205078){
 if(i0<92.9146728515625){
  s0+=0.9830283003395555;
  s1+=0.01697169966044452;
 } else {
  s0+=0.029956841838029956;
  s1+=0.9700431581619701;
 }
} else {
 if(i0<70.01118469238281){
  s0+=0.45984112974404234;
  s1+=0.5401588702559577;
 } else {
  s0+=0.018901311027105284;
  s1+=0.9810986889728948;
 }
}
if(i1<41.40980911254883){
 if(i0<96.75524139404297){
  s0+=0.983350764964853;
  s1+=0.016649235035147033;
 } else {
  s0+=0.005841741901221455;
  s1+=0.9941582580987786;
 }
} else {
 if(i1<59.04837417602539){
  s0+=0.39193842414421715;
  s1+=0.6080615758557829;
 } else {
  s0+=0.019211954104776306;
  s1+=0.9807880458952237;
 }
}
if(i0<80.31678009033203){
 if(i0<28.829879760742188){
  s0+=0.9890373171537171;
  s1+=0.010962682846282854;
 } else {
  s0+=0.7521707670043415;
  s1+=0.24782923299565845;
 }
} else {
 if(i0<97.2079849243164){
  s0+=0.34788732394366195;
  s1+=0.652112676056338;
 } else {
  s0+=0.0028540849090260435;
  s1+=0.9971459150909739;
 }
}
if(i0<80.28070068359375){
 if(i0<32.426307678222656){
  s0+=0.9873506765917172;
  s1+=0.012649323408282813;
 } else {
  s0+=0.7443481217066122;
  s1+=0.2556518782933877;
 }
} else {
 if(i0<93.71794891357422){
  s0+=0.3895513912549688;
  s1+=0.6104486087450313;
 } else {
  s0+=0.008069522036002483;
  s1+=0.9919304779639975;
 }
}
if(i0<80.001708984375){
 if(i1<44.465919494628906){
  s0+=0.9843357938074009;
  s1+=0.01566420619259909;
 } else {
  s0+=0.3795657726692209;
  s1+=0.620434227330779;
 }
} else {
 if(i1<38.041465759277344){
  s0+=0.16994510521500458;
  s1+=0.8300548947849954;
 } else {
  s0+=0.008644565673520772;
  s1+=0.9913554343264792;
 }
}
if(i1<43.00511169433594){
 if(i0<96.70057678222656){
  s0+=0.9827389685821339;
  s1+=0.017261031417866106;
 } else {
  s0+=0.006856540084388186;
  s1+=0.9931434599156118;
 }
} else {
 if(i0<67.23502349853516){
  s0+=0.4932580746315459;
  s1+=0.5067419253684541;
 } else {
  s0+=0.020113791169164195;
  s1+=0.9798862088308358;
 }
}
if(i1<42.3946533203125){
 if(i0<93.2349853515625){
  s0+=0.9836395349543888;
  s1+=0.01636046504561118;
 } else {
  s0+=0.026765230690797857;
  s1+=0.9732347693092022;
 }
} else {
 if(i0<70.82087707519531){
  s0+=0.4675720620842572;
  s1+=0.5324279379157428;
 } else {
  s0+=0.017932489451476793;
  s1+=0.9820675105485233;
 }
}
if(i1<41.394622802734375){
 if(i0<96.70057678222656){
  s0+=0.9832640819579381;
  s1+=0.016735918042061846;
 } else {
  s0+=0.005208333333333333;
  s1+=0.9947916666666666;
 }
} else {
 if(i0<70.01118469238281){
  s0+=0.47933247753530167;
  s1+=0.5206675224646984;
 } else {
  s0+=0.020516108350697226;
  s1+=0.9794838916493027;
 }
}
if(i1<41.73090362548828){
 if(i1<23.22881317138672){
  s0+=0.9793815093324452;
  s1+=0.020618490667554862;
 } else {
  s0+=0.7261390496372973;
  s1+=0.27386095036270275;
 }
} else {
 if(i0<66.66114807128906){
  s0+=0.49710815500289185;
  s1+=0.5028918449971082;
 } else {
  s0+=0.026456291337955216;
  s1+=0.9735437086620448;
 }
}
if(i0<81.41800689697266){
 if(i1<51.53511047363281){
  s0+=0.9808051926116279;
  s1+=0.01919480738837212;
 } else {
  s0+=0.2374015748031496;
  s1+=0.7625984251968504;
 }
} else {
 if(i0<96.92715454101562){
  s0+=0.34385964912280703;
  s1+=0.656140350877193;
 } else {
  s0+=0.0034778905529136207;
  s1+=0.9965221094470864;
 }
}
if(i1<41.39433670043945){
 if(i1<23.230804443359375){
  s0+=0.9801832106151742;
  s1+=0.019816789384825746;
 } else {
  s0+=0.7354566135846604;
  s1+=0.26454338641533964;
 }
} else {
 if(i0<70.00886535644531){
  s0+=0.4896049896049896;
  s1+=0.5103950103950103;
 } else {
  s0+=0.021018852787805856;
  s1+=0.9789811472121941;
 }
}
if(i0<80.31678009033203){
 if(i1<52.25349426269531){
  s0+=0.981111479661068;
  s1+=0.018888520338931944;
 } else {
  s0+=0.21296676241280263;
  s1+=0.7870332375871973;
 }
} else {
 if(i1<38.184425354003906){
  s0+=0.16728797763280523;
  s1+=0.8327120223671948;
 } else {
  s0+=0.009110848658680615;
  s1+=0.9908891513413194;
 }
}
if(i0<79.16609191894531){
 if(i1<48.65005111694336){
  s0+=0.9824873741961269;
  s1+=0.017512625803873125;
 } else {
  s0+=0.3018867924528302;
  s1+=0.6981132075471698;
 }
} else {
 if(i1<38.184425354003906){
  s0+=0.17417279411764705;
  s1+=0.8258272058823529;
 } else {
  s0+=0.011840240140081714;
  s1+=0.9881597598599183;
 }
}
if(i1<41.394622802734375){
 if(i1<23.230804443359375){
  s0+=0.9794648812272331;
  s1+=0.020535118772766918;
 } else {
  s0+=0.7366024233552899;
  s1+=0.2633975766447101;
 }
} else {
 if(i0<72.76004791259766){
  s0+=0.4746993987975952;
  s1+=0.5253006012024048;
 } else {
  s0+=0.01822510467120926;
  s1+=0.9817748953287907;
 }
}
if(i0<79.99603271484375){
 if(i0<27.462610244750977){
  s0+=0.9893795270662329;
  s1+=0.010620472933767106;
 } else {
  s0+=0.7664978094194962;
  s1+=0.23350219058050384;
 }
} else {
 if(i1<38.18671798706055){
  s0+=0.16390088656512844;
  s1+=0.8360991134348715;
 } else {
  s0+=0.011409060626249167;
  s1+=0.9885909393737509;
 }
}
if(i0<79.99324035644531){
 if(i0<28.427284240722656){
  s0+=0.9890401169400682;
  s1+=0.010959883059931785;
 } else {
  s0+=0.7545136459062282;
  s1+=0.24548635409377187;
 }
} else {
 if(i0<97.2079849243164){
  s0+=0.3402714932126697;
  s1+=0.6597285067873303;
 } else {
  s0+=0.003077363486724397;
  s1+=0.9969226365132756;
 }
}
if(i1<41.394622802734375){
 if(i0<98.05915832519531){
  s0+=0.9837006065173265;
  s1+=0.01629939348267354;
 } else {
  s0+=0.0022081148219707425;
  s1+=0.9977918851780293;
 }
} else {
 if(i1<58.93434143066406){
  s0+=0.38779606583701326;
  s1+=0.6122039341629868;
 } else {
  s0+=0.020272072552680716;
  s1+=0.9797279274473193;
 }
}
if(i1<41.40980911254883){
 if(i0<96.1222152709961){
  s0+=0.9831357224899574;
  s1+=0.016864277510042602;
 } else {
  s0+=0.009379310344827587;
  s1+=0.9906206896551724;
 }
} else {
 if(i1<57.173828125){
  s0+=0.39066990935219986;
  s1+=0.6093300906478001;
 } else {
  s0+=0.02213486026835313;
  s1+=0.9778651397316469;
 }
}
if(i1<41.377445220947266){
 if(i0<96.70057678222656){
  s0+=0.9832040480370301;
  s1+=0.016795951962969936;
 } else {
  s0+=0.005482456140350877;
  s1+=0.9945175438596491;
 }
} else {
 if(i0<66.66268157958984){
  s0+=0.49843260188087773;
  s1+=0.5015673981191222;
 } else {
  s0+=0.029402440957362497;
  s1+=0.9705975590426374;
 }
}
if(i1<40.92188262939453){
 if(i1<23.22881317138672){
  s0+=0.9797897054211971;
  s1+=0.02021029457880287;
 } else {
  s0+=0.7407407407407407;
  s1+=0.25925925925925924;
 }
} else {
 if(i1<57.19087219238281){
  s0+=0.4150464919695689;
  s1+=0.5849535080304311;
 } else {
  s0+=0.02500865351332641;
  s1+=0.9749913464866736;
 }
}
if(i0<81.25906372070312){
 if(i1<48.583770751953125){
  s0+=0.9820271137979913;
  s1+=0.0179728862020087;
 } else {
  s0+=0.29793510324483774;
  s1+=0.7020648967551623;
 }
} else {
 if(i0<97.2079849243164){
  s0+=0.3618864292589028;
  s1+=0.6381135707410972;
 } else {
  s0+=0.0030309438218086983;
  s1+=0.9969690561781913;
 }
}
if(i1<41.845088958740234){
 if(i0<96.70057678222656){
  s0+=0.9833932077490259;
  s1+=0.016606792250974098;
 } else {
  s0+=0.007904061052057782;
  s1+=0.9920959389479422;
 }
} else {
 if(i0<69.96110534667969){
  s0+=0.4825923942153187;
  s1+=0.5174076057846813;
 } else {
  s0+=0.02042614546619673;
  s1+=0.9795738545338033;
 }
}
if(i0<81.42636108398438){
 if(i0<28.486995697021484){
  s0+=0.9890054324573118;
  s1+=0.010994567542688224;
 } else {
  s0+=0.751924422673198;
  s1+=0.24807557732680197;
 }
} else {
 if(i1<40.847232818603516){
  s0+=0.14783001808318263;
  s1+=0.8521699819168174;
 } else {
  s0+=0.007614213197969543;
  s1+=0.9923857868020305;
 }
}
if(i1<43.00470733642578){
 if(i1<23.630828857421875){
  s0+=0.979622053877426;
  s1+=0.020377946122574014;
 } else {
  s0+=0.720157196659571;
  s1+=0.279842803340429;
 }
} else {
 if(i1<60.44602966308594){
  s0+=0.3557320738514384;
  s1+=0.6442679261485617;
 } else {
  s0+=0.014302851342622496;
  s1+=0.9856971486573775;
 }
}
if(i0<80.92070770263672){
 if(i1<53.56859588623047){
  s0+=0.9806354464689053;
  s1+=0.019364553531094713;
 } else {
  s0+=0.19484491044124072;
  s1+=0.8051550895587593;
 }
} else {
 if(i0<97.234130859375){
  s0+=0.34773969200198707;
  s1+=0.6522603079980129;
 } else {
  s0+=0.0033402032549214697;
  s1+=0.9966597967450785;
 }
}
if(i0<80.82121276855469){
 if(i1<52.19689178466797){
  s0+=0.981119210253192;
  s1+=0.018880789746807947;
 } else {
  s0+=0.2244569589702333;
  s1+=0.7755430410297667;
 }
} else {
 if(i1<32.49144744873047){
  s0+=0.1639068200954252;
  s1+=0.8360931799045748;
 } else {
  s0+=0.01203960396039604;
  s1+=0.987960396039604;
 }
}
if(i0<80.82296752929688){
 if(i0<27.993953704833984){
  s0+=0.9890684472388573;
  s1+=0.01093155276114265;
 } else {
  s0+=0.7600445651417033;
  s1+=0.23995543485829676;
 }
} else {
 if(i1<38.10382080078125){
  s0+=0.16095238095238096;
  s1+=0.839047619047619;
 } else {
  s0+=0.008249852681202121;
  s1+=0.9917501473187978;
 }
}
if(i1<43.00511169433594){
 if(i0<96.724609375){
  s0+=0.9821391761952687;
  s1+=0.01786082380473136;
 } else {
  s0+=0.005778894472361809;
  s1+=0.9942211055276382;
 }
} else {
 if(i0<70.67826843261719){
  s0+=0.4521538016767852;
  s1+=0.5478461983232148;
 } else {
  s0+=0.016310227178164268;
  s1+=0.9836897728218358;
 }
}
if(i0<80.15615844726562){
 if(i0<29.165830612182617){
  s0+=0.988354866048612;
  s1+=0.011645133951388035;
 } else {
  s0+=0.7518059855521155;
  s1+=0.24819401444788441;
 }
} else {
 if(i0<96.6630859375){
  s0+=0.3637222484648087;
  s1+=0.6362777515351913;
 } else {
  s0+=0.0038885746606334842;
  s1+=0.9961114253393665;
 }
}
if(i1<41.39433670043945){
 if(i0<93.48221588134766){
  s0+=0.9839925473099685;
  s1+=0.01600745269003142;
 } else {
  s0+=0.02640845070422535;
  s1+=0.9735915492957746;
 }
} else {
 if(i1<58.93434143066406){
  s0+=0.376392901361948;
  s1+=0.623607098638052;
 } else {
  s0+=0.01895943562610229;
  s1+=0.9810405643738977;
 }
}
if(i1<41.377445220947266){
 if(i0<96.70057678222656){
  s0+=0.9835043899117154;
  s1+=0.01649561008828465;
 } else {
  s0+=0.00832639467110741;
  s1+=0.9916736053288926;
 }
} else {
 if(i1<57.195377349853516){
  s0+=0.3982845832416978;
  s1+=0.6017154167583022;
 } else {
  s0+=0.02402376910016978;
  s1+=0.9759762308998302;
 }
}
if(i1<41.394622802734375){
 if(i1<24.216060638427734){
  s0+=0.9785023015033089;
  s1+=0.02149769849669119;
 } else {
  s0+=0.7230381115415804;
  s1+=0.2769618884584197;
 }
} else {
 if(i0<70.45181274414062){
  s0+=0.49522087315939034;
  s1+=0.5047791268406097;
 } else {
  s0+=0.02067758867504374;
  s1+=0.9793224113249562;
 }
}
if(i0<80.92941284179688){
 if(i1<44.61708068847656){
  s0+=0.9832344862788945;
  s1+=0.016765513721105466;
 } else {
  s0+=0.36120576671035387;
  s1+=0.6387942332896461;
 }
} else {
 if(i1<37.49266052246094){
  s0+=0.15450121654501217;
  s1+=0.8454987834549879;
 } else {
  s0+=0.00990847258376018;
  s1+=0.9900915274162398;
 }
}
if(i1<42.394981384277344){
 if(i0<96.68724060058594){
  s0+=0.9823555814348448;
  s1+=0.017644418565155154;
 } else {
  s0+=0.005595523581135092;
  s1+=0.9944044764188649;
 }
} else {
 if(i0<70.6820068359375){
  s0+=0.47075688073394495;
  s1+=0.529243119266055;
 } else {
  s0+=0.019529660672145822;
  s1+=0.9804703393278542;
 }
}
if(i1<40.78263854980469){
 if(i0<96.67149353027344){
  s0+=0.9834715776908929;
  s1+=0.016528422309107074;
 } else {
  s0+=0.006311745334796927;
  s1+=0.993688254665203;
 }
} else {
 if(i1<59.58594512939453){
  s0+=0.38294314381270905;
  s1+=0.617056856187291;
 } else {
  s0+=0.01656222023276634;
  s1+=0.9834377797672337;
 }
}
if(i0<80.24142456054688){
 if(i1<52.73722839355469){
  s0+=0.9801693391187848;
  s1+=0.019830660881215188;
 } else {
  s0+=0.21844863731656183;
  s1+=0.7815513626834382;
 }
} else {
 if(i1<38.191043853759766){
  s0+=0.15707294336984387;
  s1+=0.8429270566301561;
 } else {
  s0+=0.010233796488892586;
  s1+=0.9897662035111074;
 }
}
if(i1<40.87636947631836){
 if(i0<93.42716217041016){
  s0+=0.9842284739982949;
  s1+=0.01577152600170503;
 } else {
  s0+=0.02236842105263158;
  s1+=0.9776315789473684;
 }
} else {
 if(i1<57.87818145751953){
  s0+=0.392544306376044;
  s1+=0.607455693623956;
 } else {
  s0+=0.024239253640247625;
  s1+=0.9757607463597524;
 }
}
if(i1<41.448875427246094){
 if(i0<96.91268157958984){
  s0+=0.9832178458546074;
  s1+=0.016782154145392657;
 } else {
  s0+=0.003691983122362869;
  s1+=0.9963080168776371;
 }
} else {
 if(i1<58.769134521484375){
  s0+=0.39692183070068854;
  s1+=0.6030781692993115;
 } else {
  s0+=0.02099031216361679;
  s1+=0.9790096878363832;
 }
}
if(i0<81.0031967163086){
 if(i0<28.753931045532227){
  s0+=0.9888013272501037;
  s1+=0.011198672749896308;
 } else {
  s0+=0.7577715557470026;
  s1+=0.24222844425299733;
 }
} else {
 if(i0<97.15748596191406){
  s0+=0.3376560999039385;
  s1+=0.6623439000960615;
 } else {
  s0+=0.004473478662216857;
  s1+=0.9955265213377832;
 }
}
if(i1<41.396278381347656){
 if(i1<23.231197357177734){
  s0+=0.9794696595796504;
  s1+=0.020530340420349586;
 } else {
  s0+=0.730385746976136;
  s1+=0.269614253023864;
 }
} else {
 if(i0<70.67826843261719){
  s0+=0.49748876553000265;
  s1+=0.5025112344699973;
 } else {
  s0+=0.020378457059679767;
  s1+=0.9796215429403202;
 }
}
if(i0<79.99671173095703){
 if(i1<44.83842468261719){
  s0+=0.9832701885828615;
  s1+=0.016729811417138477;
 } else {
  s0+=0.3745328350240256;
  s1+=0.6254671649759743;
 }
} else {
 if(i1<38.18671798706055){
  s0+=0.1592920353982301;
  s1+=0.8407079646017699;
 } else {
  s0+=0.011072896569079775;
  s1+=0.9889271034309203;
 }
}
if(i0<80.0013427734375){
 if(i0<27.561172485351562){
  s0+=0.989827769342552;
  s1+=0.010172230657448015;
 } else {
  s0+=0.7638586956521739;
  s1+=0.2361413043478261;
 }
} else {
 if(i0<96.95153045654297){
  s0+=0.3654197080291971;
  s1+=0.6345802919708029;
 } else {
  s0+=0.004134293249697056;
  s1+=0.995865706750303;
 }
}
if(i1<42.47425842285156){
 if(i0<98.05528259277344){
  s0+=0.9822250003034312;
  s1+=0.0177749996965688;
 } else {
  s0+=0.0021058173203474598;
  s1+=0.9978941826796526;
 }
} else {
 if(i1<59.176307678222656){
  s0+=0.38401219777826184;
  s1+=0.6159878022217382;
 } else {
  s0+=0.017548548013539996;
  s1+=0.98245145198646;
 }
}
if(i0<80.31436157226562){
 if(i1<53.56987380981445){
  s0+=0.9802543890862377;
  s1+=0.01974561091376222;
 } else {
  s0+=0.1935483870967742;
  s1+=0.8064516129032258;
 }
} else {
 if(i1<39.861419677734375){
  s0+=0.1563532261616384;
  s1+=0.8436467738383616;
 } else {
  s0+=0.008296270954498803;
  s1+=0.9917037290455012;
 }
}
if(i1<42.0635986328125){
 if(i0<96.70057678222656){
  s0+=0.9830839969144238;
  s1+=0.016916003085576146;
 } else {
  s0+=0.005392289026691831;
  s1+=0.9946077109733081;
 }
} else {
 if(i0<73.47364807128906){
  s0+=0.4650558586645882;
  s1+=0.5349441413354118;
 } else {
  s0+=0.014586577071211996;
  s1+=0.985413422928788;
 }
}
if(i0<80.100830078125){
 if(i0<28.40994644165039){
  s0+=0.9888469079058414;
  s1+=0.011153092094158584;
 } else {
  s0+=0.7616349082666289;
  s1+=0.23836509173337112;
 }
} else {
 if(i0<97.2079849243164){
  s0+=0.36042240587695135;
  s1+=0.6395775941230487;
 } else {
  s0+=0.0033416281549946674;
  s1+=0.9966583718450054;
 }
}
if(i0<79.2782211303711){
 if(i1<52.36473846435547){
  s0+=0.9810563299506788;
  s1+=0.018943670049321154;
 } else {
  s0+=0.22062448644207067;
  s1+=0.7793755135579293;
 }
} else {
 if(i0<97.0633773803711){
  s0+=0.36970474967907574;
  s1+=0.6302952503209243;
 } else {
  s0+=0.0038632136214050653;
  s1+=0.9961367863785949;
 }
}
if(i1<41.396278381347656){
 if(i1<21.304546356201172){
  s0+=0.981448222907638;
  s1+=0.01855177709236195;
 } else {
  s0+=0.7513727670813929;
  s1+=0.24862723291860708;
 }
} else {
 if(i1<58.76036834716797){
  s0+=0.38664007976071785;
  s1+=0.6133599202392822;
 } else {
  s0+=0.021116805089238383;
  s1+=0.9788831949107616;
 }
}
if(i0<79.95075988769531){
 if(i0<29.319664001464844){
  s0+=0.9886893784981721;
  s1+=0.011310621501827947;
 } else {
  s0+=0.7553096687954849;
  s1+=0.24469033120451508;
 }
} else {
 if(i1<38.0989875793457){
  s0+=0.16131284916201116;
  s1+=0.8386871508379888;
 } else {
  s0+=0.009842031262922836;
  s1+=0.9901579687370772;
 }
}
if(i1<41.30156326293945){
 if(i1<25.370635986328125){
  s0+=0.9779552029510972;
  s1+=0.022044797048902814;
 } else {
  s0+=0.713363590519415;
  s1+=0.286636409480585;
 }
} else {
 if(i1<57.151554107666016){
  s0+=0.40163572060123787;
  s1+=0.5983642793987621;
 } else {
  s0+=0.024216401889222843;
  s1+=0.9757835981107772;
 }
}
if(i0<80.0024642944336){
 if(i1<53.29399108886719){
  s0+=0.9804161492929561;
  s1+=0.019583850707043916;
 } else {
  s0+=0.2010443864229765;
  s1+=0.7989556135770235;
 }
} else {
 if(i0<97.01478576660156){
  s0+=0.3631863186318632;
  s1+=0.6368136813681368;
 } else {
  s0+=0.0037922152261018888;
  s1+=0.9962077847738982;
 }
}
if(i1<41.40599060058594){
 if(i1<23.231708526611328){
  s0+=0.9797066093153147;
  s1+=0.020293390684685265;
 } else {
  s0+=0.7321091892778097;
  s1+=0.26789081072219034;
 }
} else {
 if(i1<55.72980499267578){
  s0+=0.4236658932714617;
  s1+=0.5763341067285382;
 } else {
  s0+=0.029858132629495215;
  s1+=0.9701418673705048;
 }
}
if(i1<42.08797073364258){
 if(i0<96.68724060058594){
  s0+=0.9826363161538741;
  s1+=0.017363683846125817;
 } else {
  s0+=0.007281553398058253;
  s1+=0.9927184466019418;
 }
} else {
 if(i1<55.73078155517578){
  s0+=0.40152822282474737;
  s1+=0.5984717771752527;
 } else {
  s0+=0.026546456298522415;
  s1+=0.9734535437014776;
 }
}
if(i1<41.394622802734375){
 if(i0<96.83836364746094){
  s0+=0.9833073048012008;
  s1+=0.016692695198799245;
 } else {
  s0+=0.008547008547008548;
  s1+=0.9914529914529915;
 }
} else {
 if(i1<57.3011589050293){
  s0+=0.3998692525604707;
  s1+=0.6001307474395293;
 } else {
  s0+=0.022032877183922887;
  s1+=0.9779671228160771;
 }
}
if(i0<80.92941284179688){
 if(i1<48.178138732910156){
  s0+=0.9818039700428998;
  s1+=0.01819602995710027;
 } else {
  s0+=0.3042353701907533;
  s1+=0.6957646298092467;
 }
} else {
 if(i0<96.95153045654297){
  s0+=0.3468208092485549;
  s1+=0.653179190751445;
 } else {
  s0+=0.003875149721693793;
  s1+=0.9961248502783062;
 }
}
if(i0<80.1572036743164){
 if(i1<44.46601104736328){
  s0+=0.983462519712847;
  s1+=0.01653748028715301;
 } else {
  s0+=0.37869667626275844;
  s1+=0.6213033237372415;
 }
} else {
 if(i0<97.8167724609375){
  s0+=0.3530711444984534;
  s1+=0.6469288555015467;
 } else {
  s0+=0.0026986719693203607;
  s1+=0.9973013280306796;
 }
}
if(i1<41.394622802734375){
 if(i1<23.227935791015625){
  s0+=0.9800759074006515;
  s1+=0.01992409259934845;
 } else {
  s0+=0.7295498044437048;
  s1+=0.27045019555629524;
 }
} else {
 if(i0<66.66268157958984){
  s0+=0.4955344281186978;
  s1+=0.5044655718813023;
 } else {
  s0+=0.024788821346806662;
  s1+=0.9752111786531933;
 }
}
if(i1<41.914093017578125){
 if(i1<23.227935791015625){
  s0+=0.9797510182125567;
  s1+=0.020248981787443325;
 } else {
  s0+=0.7256535947712418;
  s1+=0.2743464052287582;
 }
} else {
 if(i1<58.912139892578125){
  s0+=0.38981244671781756;
  s1+=0.6101875532821824;
 } else {
  s0+=0.01768346595932803;
  s1+=0.982316534040672;
 }
}
 float max_s=s0;
 int cls=1;
 if (max_s < s1) {
  max_s = s1;
  cls=2;
 }
 WRITE_IMAGE (out, POS_out_INSTANCE(x,y,z,0), cls);
}
