import os
from pathlib import Path

import cv2 as cv
import matplotlib.pyplot as plt
import numpy as np
import segmentation_models_pytorch as smp
import torch

from src.utils.labelme_to_mask import parse_labelme_to_mask
from src.utils.max_pool import crop_image_to_multiple, max_pool_resize

device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
assert device.type == 'cuda', (
    'GPU not found, please use a GPU to train the model or disable this line.'
)


class Data(torch.utils.data.Dataset):
    def __init__(self, scale):
        self.scale = scale
        self.images, self.masks = self.load_data()

    def load_data(self):
        DATA_DIR = 'data/OCR/20250415/images/images_biglabel'

        # Load all json files
        json_files = [f for f in os.listdir(DATA_DIR) if f.endswith('_prep.json')]
        image_files = [f.replace('.json', '.png') for f in json_files]

        images = [cv.imread(str(Path(DATA_DIR) / f), cv.IMREAD_GRAYSCALE) for f in image_files]
        masks = [
            parse_labelme_to_mask(
                str(Path(DATA_DIR) / f),
                unknown_label=255,
                clean_label=0,
                text_label=1,
            )
            for f in json_files
        ]

        masks = [crop_image_to_multiple(mask, self.scale) for mask in masks]
        images = [crop_image_to_multiple(img, self.scale) for img in images]
        masks = [max_pool_resize(mask, 1 / self.scale) for mask in masks]
        images = [cv.resize(img, masks[0].shape[::-1]) for img in images]
        return images, masks

    def __getitem__(self, index):
        image = self.images[index]
        mask = self.masks[index]

        # Convert to PyTorch format
        image = image[np.newaxis, :, :]
        mask = mask[np.newaxis, :, :]

        # Convert to tensor
        image = torch.from_numpy(image).float()
        mask = torch.from_numpy(mask).float()

        return image, mask

    def __len__(self):
        return len(self.images)


def train(run):
    Path(f'src/detections/free_space/model/unet/run{run:02d}/checkpoints').mkdir(
        parents=True, exist_ok=False
    )
    Path(f'src/detections/free_space/model/unet/run{run:02d}/vis').mkdir(
        parents=True, exist_ok=False
    )
    with open(f'src/detections/free_space/model/unet/run{run:02d}/loss.txt', 'w') as f:
        f.write('')

    dataset = Data(4)
    loader = torch.utils.data.DataLoader(dataset, batch_size=1, shuffle=True)

    model = smp.Unet(
        encoder_name='mobilenet_v2',
        encoder_weights=None,
        in_channels=1,
        classes=1,
    )
    model.to(device)
    model.train()

    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    loss_function = smp.losses.DiceLoss(mode='binary', ignore_index=255)

    # Training loop
    for epoch in range(51):
        train_loss = 0.0
        for _, (image, mask) in enumerate(loader):
            cpu_image = image[0, 0, :, :]
            cpu_mask = mask[0, 0, :, :]
            image = image.to(device)
            mask = mask.to(device)

            # Forward pass
            optimizer.zero_grad()
            output = model(image)

            # Calculate loss
            loss = loss_function(output, mask)
            train_loss += loss.item()

            # Backward pass
            loss.backward()

            # Update weights
            optimizer.step()

        print(f'Epoch {epoch:4d} | Train loss: {train_loss / len(loader):.6f}')
        with open(f'src/detections/free_space/model/unet/run{run:02d}/loss.txt', 'a') as f:
            f.write(f'Epoch {epoch:4d} | Train loss: {train_loss / len(loader):.6f}\n')

        if epoch % 5 == 0:
            torch.save(
                model.state_dict(),
                f'src/detections/free_space/model/unet/run{run:02d}/checkpoints/unet_model_{epoch}.pth',
            )
        cpu_output = output.cpu().detach().numpy()[0, 0, :, :]
        fig, ax = plt.subplots(3, 1, sharex=True, sharey=True, figsize=(20, 8))
        ax[0].imshow(cpu_image, cmap='viridis')
        ax[0].set_title('Image')
        ax[1].imshow(cpu_mask, cmap='viridis')
        ax[1].set_title('Mask')
        ax[2].imshow(cpu_output, cmap='viridis')
        ax[2].set_title('Prediction')
        # save to file
        fig.savefig(
            f'src/detections/free_space/model/unet/run{run:02d}/vis/unet_model_{epoch}.png'
        )
        plt.close(fig)


def infer(img, visualize=False):
    model = smp.Unet(
        encoder_name='mobilenet_v2',
        encoder_weights=None,
        in_channels=1,
        classes=1,
    )
    model.load_state_dict(
        torch.load('src/detections/free_space/model/unet/run03/checkpoints/unet_model_15.pth')
    )
    model.to(device)
    model.eval()

    img_tensor = img[np.newaxis, np.newaxis, :, :]
    image = torch.from_numpy(img_tensor).float().to(device)

    # Forward pass
    with torch.no_grad():  # Disable gradient computation for inference
        output = model(image)

    # Convert to numpy
    cpu_output = output.cpu().detach().numpy()[0, 0, :, :]

    if visualize:
        fig, ax = plt.subplots(1, 2, sharex=True, sharey=True)
        ax[0].imshow(img, cmap='viridis')
        ax[0].set_title('Image')
        ax[1].imshow(cpu_output, cmap='viridis')
        ax[1].set_title('Prediction')
        fig.tight_layout()
        plt.show()

    return cpu_output


if __name__ == '__main__':
    # train(3)
    test(cv.imread())
