import os
import time
from functools import partial
from pathlib import Path

import cv2 as cv
import joblib
import matplotlib.pyplot as plt
import numpy as np
from skimage import feature, future, segmentation
from sklearn.ensemble import RandomForestClassifier

from src.utils.labelme_to_mask import parse_labelme_to_mask
from src.utils.max_pool import crop_image_to_multiple, max_pool_resize

sigma_min = 1
sigma_max = 16
features_func = partial(
    feature.multiscale_basic_features,
    intensity=True,
    edges=False,
    texture=True,
    sigma_min=sigma_min,
    sigma_max=sigma_max,
    channel_axis=None,  # Use None for grayscale images
)


def main():
    # Labelme labels
    # Polygons only
    # Labels: clean, text
    DATA_DIR = 'data/OCR/20250415/images/images_cropped'

    # Load all json files
    json_files = [f for f in os.listdir(DATA_DIR) if f.endswith('_prep.json')]
    image_files = [f.replace('.json', '.png') for f in json_files]

    images = [cv.imread(str(Path(DATA_DIR) / f), cv.IMREAD_GRAYSCALE) for f in image_files]
    masks = [parse_labelme_to_mask(str(Path(DATA_DIR) / f)) for f in json_files]

    # # Crop
    # y = 242
    # h = 2000
    # img = img[y : y + h, :]
    # training_labels = training_labels[y : y + h, :]

    # downscale
    SCALE = 4
    # Cut off right side to the nearest number of whole SCALE pixels
    masks = [crop_image_to_multiple(mask, SCALE) for mask in masks]
    images = [crop_image_to_multiple(img, SCALE) for img in images]
    masks = [max_pool_resize(mask, 1 / SCALE) for mask in masks]
    images = [cv.resize(img, masks[0].shape[::-1]) for img in images]

    features = np.concatenate([features_func(img) for img in images], axis=0)
    training_labels = np.concatenate(masks, axis=0)
    clf = RandomForestClassifier(n_estimators=10, n_jobs=-1, max_depth=10, bootstrap=False)
    clf = future.fit_segmenter(training_labels, features, clf)

    # Save to file
    joblib.dump(
        clf, f'src/detections/free_space/model/free_space_segmenter_s{SCALE}.joblib', compress=3
    )

    # Load from file
    clf = joblib.load(f'src/detections/free_space/model/free_space_segmenter_s{SCALE}.joblib')

    for i in range(len(images)):
        full_img = images[i]
        start = time.perf_counter()
        result = future.predict_segmenter(features_func(full_img), clf)
        end = time.perf_counter()
        print(f'Prediction time: {end - start:.2f} seconds')

        fig, ax = plt.subplots(3, 1, sharex=True, sharey=True, figsize=(9, 4))
        ax[0].imshow(full_img, cmap='gray')
        ax[0].set_title('Image and training data')
        ax[0].contour(masks[i])
        ax[1].imshow(segmentation.mark_boundaries(full_img, result, mode='outer'))
        ax[1].set_title('Image and segmentation boundaries')
        ax[2].imshow(result)
        ax[2].set_title('Segmentation')
        fig.tight_layout()
        plt.show()


if __name__ == '__main__':
    main()
